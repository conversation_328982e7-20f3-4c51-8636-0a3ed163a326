\[!\[](https://mermaid.ink/img/pako:eNp9kttq4zAQhl9FzMXSQpq1FbtJfFFI7CR1D1Bo6cXiG9WeNC6xFGQ53TTk3Ts-7FpQiEDwfxrNP6NBR0hVhhDAuxa7DXuJEsloldVbe5DAXKvPEvXvR\_WWb5G9Vjj8KNnz0yyB9m69Zq1EmSXyh8PsKWYrYfBTHOyc-YUVuDxnECqN7DFPtaJG9nmKJbt4EFrscctcfmmbhr2Merno5bKXq3M1Y2mQtMmVZA\_igNqucnsuMxJGsF8slmstSqOr1FQa7ey4l3e9vP\_hOWNXVzds3sK8gdCGyIaFDUsbumdGDXSdL224baCrH1LrEe0F7SXtVRPsWo4JWBiFbLFHacom1D3hrg49H2TaOsOAflSeQUADwAEUqAtRIxzr6wmYDRY0lIBkhmtRbU09oROl7YT8o1TxL1Or6n0DwVpsS6Jql9F\_iXJBwy7-n2oaGupQVdJAwKdOYwLBEf5CMHLHQ8fj\_pg73PUmnjeAAwSuO\_RdbzTmE-5fc-7709MAvpqyznBKFteeN504jjNyxqPTN15X2MU?type=png)](https://mermaid.live/edit#pako:eNp9kttq4zAQhl9FzMXSQpq1FbtJfFFI7CR1D1Bo6cXiG9WeNC6xFGQ53TTk3Ts-7FpQiEDwfxrNP6NBR0hVhhDAuxa7DXuJEsloldVbe5DAXKvPEvXvR\_WWb5G9Vjj8KNnz0yyB9m69Zq1EmSXyh8PsKWYrYfBTHOyc-YUVuDxnECqN7DFPtaJG9nmKJbt4EFrscctcfmmbhr2Merno5bKXq3M1Y2mQtMmVZA\_igNqucnsuMxJGsF8slmstSqOr1FQa7ey4l3e9vP\_hOWNXVzds3sK8gdCGyIaFDUsbumdGDXSdL224baCrH1LrEe0F7SXtVRPsWo4JWBiFbLFHacom1D3hrg49H2TaOsOAflSeQUADwAEUqAtRIxzr6wmYDRY0lIBkhmtRbU09oROl7YT8o1TxL1Or6n0DwVpsS6Jql9F\_iXJBwy7-n2oaGupQVdJAwKdOYwLBEf5CMHLHQ8fj\_pg73PUmnjeAAwSuO\_RdbzTmE-5fc-7709MAvpqyznBKFteeN504jjNyxqPTN15X2MU)



graph TD

&nbsp;   subgraph "Browser/Mobile Vue.js SPA"

&nbsp;       A

&nbsp;   end



&nbsp;   subgraph "API Gateway"

&nbsp;       B(API Gateway)

&nbsp;   end



&nbsp;   subgraph "Core Microservices (Laravel 12)"

&nbsp;       C

&nbsp;       D

&nbsp;       E

&nbsp;       F

&nbsp;       G

&nbsp;   end



&nbsp;   subgraph "Integration Layer"

&nbsp;       H

&nbsp;   end



&nbsp;   subgraph "Data \& Infrastructure"

&nbsp;       I

&nbsp;       J

&nbsp;       K

&nbsp;   end



&nbsp;   A --> B

&nbsp;   B --> C

&nbsp;   B --> D

&nbsp;   B --> E

&nbsp;   B --> F

&nbsp;   B --> G

&nbsp;   D --> H

&nbsp;   F --> H

&nbsp;   H --> K

&nbsp;   C \& D \& E \& F \& G --> I

&nbsp;   I -- CDC Events --> J

&nbsp;   J -- Sync --> H



gantt

&nbsp;   title Jadwal Pelaksanaan Proyek Pengembangan SIASN BKD Jateng

&nbsp;   dateFormat YYYY-MM-DD

&nbsp;   axisFormat %d %b

&nbsp;   

&nbsp;   section Fase 1: Fondasi \& Analisis

&nbsp;   Sprint 0: Kick-off, Setup \& Analisis :critical, s0, 2025-10-01, 7d

&nbsp;   Laporan Awal :milestone, la, 2025-10-08, 0d



&nbsp;   section Fase 2: Pengembangan Inti

&nbsp;   Sprint 1: Auth \& User Service :critical, s1, after s0, 7d

&nbsp;   Sprint 2: PDM Backend \& RBAC :critical, s2, after s1, 7d



&nbsp;   section Fase 3: Fitur \& Integrasi

&nbsp;   Sprint 3: Modul Pemberkasan :critical, s3, after s2, 7d

&nbsp;   Sprint 4: Dashboard \& Integrasi Awal :critical, s4, after s3, 7d

&nbsp;   

&nbsp;   section Fase 4: Pengujian \& UAT

&nbsp;   Sprint 5: Integration Testing :critical, s5, after s4, 7d

&nbsp;   Sprint 6: UAT \& Bug Fixing :critical, s6, after s5, 7d



&nbsp;   section Fase 5: Deployment \& Serah Terima

&nbsp;   Sprint 7: Deployment \& Training :critical, s7, after s6, 7d

&nbsp;   Sprint 8: Finalisasi \& Handover :critical, s8, after s7, 7d

&nbsp;   Laporan Akhir \& Serah Terima :milestone, lf, 2025-11-30, 0d

